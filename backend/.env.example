# Google AI API Configuration
GOOGLE_API_KEY=your_google_api_key_here

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=medical-bot-index

# Application Configuration
APP_NAME=Medical Bot API
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Document Processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=100
MAX_DOCUMENTS=1000
