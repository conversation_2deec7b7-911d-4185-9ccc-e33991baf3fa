# File generated from our OpenAPI spec by <PERSON><PERSON>less. See CONTRIBUTING.md for details.

from typing import List, Generic, TypeVar, Optional

from ..._models import GenericModel
from .chat_completion import Choice, ChatCompletion
from .chat_completion_message import ChatCompletionMessage
from .parsed_function_tool_call import ParsedFunctionToolCall

__all__ = ["ParsedChatCompletion", "ParsedChoice"]


ContentType = TypeVar("ContentType")


# we need to disable this check because we're overriding properties
# with subclasses of their types which is technically unsound as
# properties can be mutated.
# pyright: reportIncompatibleVariableOverride=false


class ParsedChatCompletionMessage(ChatCompletionMessage, GenericModel, Generic[ContentType]):
    parsed: Optional[ContentType] = None
    """The auto-parsed message contents"""

    tool_calls: Optional[List[ParsedFunctionToolCall]] = None  # type: ignore[assignment]
    """The tool calls generated by the model, such as function calls."""


class ParsedChoice(Choice, GenericModel, Generic[ContentType]):
    message: ParsedChatCompletionMessage[ContentType]
    """A chat completion message generated by the model."""


class ParsedChatCompletion(ChatCompletion, GenericModel, Generic[ContentType]):
    choices: List[ParsedChoice[ContentType]]  # type: ignore[assignment]
    """A list of chat completion choices.

    Can be more than one if `n` is greater than 1.
    """
