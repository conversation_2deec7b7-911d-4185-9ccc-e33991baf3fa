pytest_codspeed-3.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pytest_codspeed-3.2.0.dist-info/METADATA,sha256=5vVoOXpgNY5iJQ5LGfyzpU37RnCMu5fJyv0RhViM4P4,6271
pytest_codspeed-3.2.0.dist-info/RECORD,,
pytest_codspeed-3.2.0.dist-info/WHEEL,sha256=2gKwSV8jppd4UXBaW16nsWopUwibxLjhspI2Lf6lVnA,186
pytest_codspeed-3.2.0.dist-info/entry_points.txt,sha256=6uCy7WyaiRyhwx2MtbVrRAeoIym7og6QTq1-UYReNtU,45
pytest_codspeed-3.2.0.dist-info/top_level.txt,sha256=INKP_VlAcSaaySSYyL561X4EDIMapIdT_Gp2GEGGaLo,16
pytest_codspeed/__init__.py,sha256=jnvJGx8j3um7z7QkzmlIC42Zw12PDh1cw3deuUnu4w4,234
pytest_codspeed/__pycache__/__init__.cpython-312.pyc,,
pytest_codspeed/__pycache__/plugin.cpython-312.pyc,,
pytest_codspeed/__pycache__/utils.cpython-312.pyc,,
pytest_codspeed/instruments/__init__.py,sha256=pr3sbVsm3EL0wG8sC8GY3xB8mkr_bUM3XnP58gwo4QA,1397
pytest_codspeed/instruments/__pycache__/__init__.cpython-312.pyc,,
pytest_codspeed/instruments/__pycache__/walltime.cpython-312.pyc,,
pytest_codspeed/instruments/valgrind/__init__.py,sha256=jjGYM4fU-AIeXy_7NfpZf1RJwQXHyF4YMwREpTOoPJ0,3082
pytest_codspeed/instruments/valgrind/__pycache__/__init__.cpython-312.pyc,,
pytest_codspeed/instruments/valgrind/_wrapper/__init__.py,sha256=anyMBcrlDXakoGhDcQE6iklB5Q1tBACjLog-Q2WKLIQ,343
pytest_codspeed/instruments/valgrind/_wrapper/__pycache__/__init__.cpython-312.pyc,,
pytest_codspeed/instruments/valgrind/_wrapper/__pycache__/build.cpython-312.pyc,,
pytest_codspeed/instruments/valgrind/_wrapper/build.py,sha256=To7t_2pdjgOip7Wn5q6O3W5KYNHafnnkeE9pjV8A4rc,487
pytest_codspeed/instruments/valgrind/_wrapper/dist_callgrind_wrapper.cpython-312-x86_64-linux-gnu.so,sha256=8evFVQfPD5q-cA30EESAwykrgDcWIcaOQA6S5Cvehto,32600
pytest_codspeed/instruments/valgrind/_wrapper/wrapper.c,sha256=LPgxq9fZMIOCTlZvobyjjBCGg-IqBTTMnxe62Y-QL6Y,392
pytest_codspeed/instruments/valgrind/_wrapper/wrapper.h,sha256=BFwKN8rtilya4iSG_n_hRoFuyvbA4Y5A0wjBas79p9A,149
pytest_codspeed/instruments/valgrind/_wrapper/wrapper.pyi,sha256=4BT5H6Ar1ncXcd9I3fqzPN_dVoIVMETynFwjjxW-ITs,365
pytest_codspeed/instruments/walltime.py,sha256=oVvv7m7eUvk6eAXQBGML5BnZjjVzkur2j9H-TEFw5AA,7897
pytest_codspeed/plugin.py,sha256=WwC8Xh2Qoq00xhpYq-NYbj7Z_tgB_zdAXXToKbve6oY,11792
pytest_codspeed/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pytest_codspeed/utils.py,sha256=MhFcn9MLMw8m1vOl2Olbdf4d7CzmCA-lZh0uDI_6-Q4,2053
